<template>
  <view class="form-item" :class="required ? 'required' : ''">
    <view class="label-box" :style="'width:'+labelWidth">
      {{ label }}
    </view>
    <view class="value-box">
      <slot></slot>
    </view>
    <van-icon name="arrow-down" v-if="showIcon"/>
  </view>
</template>
<script>
export default {
  name: "FormItem",
  props: {
    required: <PERSON><PERSON><PERSON>,
    label: String,
    showIcon: <PERSON><PERSON><PERSON>,
    labelWidth: {
      type: String,
      default: '100px'
    }
  }
}
</script>


<style scoped lang="scss">
.form-item {
  width: 100%;
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  position: relative;
}

.required {
  .label-box {
    font-size: 14px;
    color: #323233;

    &::before {
      content: "*";
      position: absolute;
      left: -10px;
      top: 50%;
      transform: translateY(-50%);
      color: darkred;
      font-size: 14px;
    }
  }
}
</style>
