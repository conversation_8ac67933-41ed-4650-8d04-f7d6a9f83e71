<template>
  <view class="add-fight-task">
    <!-- 自定义导航栏 -->
    <CustomerNav title="创建飞行任务书"/>

    <!-- 内容主体 -->
    <view class="content">
      <view class="form-box">
        <FormItem label="注册号" required show-icon>
          <input
              class="input-box"
              v-model="formData.registrationNumber"
              placeholder="请选择注册号"
              readonly
              @click="openPicker('注册号', registrationOptions, 'registrationNumber')"
              disabled
          />
        </FormItem>
        <FormItem label="机型" required>
          <input
              class="input-box"
              v-model="formData.aircraftType"
              placeholder="请选择机型"
              readonly
          />
        </FormItem>
        <FormItem label="任务性质" required>
          <view>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in taskTypeList"
                  :class="formData.taskType === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onTaskTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <input
                class="input-box"
                v-model="formData.taskType"
                placeholder="请选择"
                readonly
                @click="openPicker('任务性质', [], 'taskType')"
                v-if="formData.taskType==='其他'"

            />
          </view>
        </FormItem>
      </view>
    </view>

    <!-- 注册号选择器 -->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
      />
    </van-popup>

  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";
import FormItem from "./compoents/FormItem.vue";

export default {
  name: "createFlightTask",
  components: {FormItem, CustomerNav},
  data() {
    return {
      // 表单数据
      formData: {
        // registrationNumber: '', // 注册号
        // aircraftType: 'BELL429', // 机型
        // taskType: '空中游览', // 任务性质
        // planTime: '今天', // 计划时间
        // planDate: '', // 具体日期
        // takeoffLocation: '复兴岛', // 起飞地
        // airspaceProductName: '', // 空域产品名称
        // packageName: '', // 套餐名称
        // expectedFlights: '', // 预计航次
        // firstFlightTime: '', // 第一架次开始时间
        // firstFlightEndTime: '', // 第一架次结束时间
        // secondFlightTime: '', // 第二架次开始时间
        // secondFlightEndTime: '', // 第二架次结束时间
        // thirdFlightTime: '', // 第三架次开始时间
        // thirdFlightEndTime: '', // 第三架次结束时间
        // interval: '15分钟', // 起降间隔
        // remark: '', // 备注
        // intelligentAnalysis: '' // 智能解析
      },
      // 选择器选项
      registrationOptions: [
        {text: 'B-7613', value: 'B-7613'},
        {text: 'B-7614', value: 'B-7614'},
        {text: 'B-7615', value: 'B-7615'}
      ],
      airspaceProductOptions: [
        {text: '黄浦江低空旅游直升机航空游览', value: '黄浦江低空旅游直升机航空游览'},
        {text: '上海市区观光飞行', value: '上海市区观光飞行'},
        {text: '浦东新区航线', value: '浦东新区航线'}
      ],
      packageOptions: [
        {text: '[日间] 15分钟单人票', value: '[日间] 15分钟单人票'},
        {text: '[日间] 30分钟双人票', value: '[日间] 30分钟双人票'},
        {text: '[夜间] 20分钟观光票', value: '[夜间] 20分钟观光票'}
      ],
      // selectModalShow: false,
      taskTypeList: [
        {text: '空中游览', value: '空中游览'},
        {text: '空中交通', value: '空中交通'},
        {text: '其他', value: '其他'},
      ],//任务类型
      pickerData: {
        show: false,
        title: "",
        list: [],
        formKey: "",
      },//下拉选择数据
    }
  },

  methods: {
    openPicker(title, list, formKey) {
      console.log(11111)
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: "",
        list: [],
        formKey: "",
      }
    },
    onPickerConfirm(ev) {
      console.log(ev.detail)
      this.formData[this.pickerData.formKey] = ev.detail.value.value;
      if (this.pickerData.formKey === "registrationNumber") {
        this.formData.aircraftType = ev.detail.value.value;
      }
      this.closePicker();
    },
    //任务性质按钮
    onTaskTypeChange(item) {
      console.log(1111)
      this.formData.taskType = item.value;
      console.log(this.formData.taskType)
    },

    // 提交表单
    submitForm() {
      // 表单验证
      if (!this.formData.registrationNumber) {
        uni.showToast({
          title: '请选择注册号',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.airspaceProductName) {
        uni.showToast({
          title: '请选择空域产品',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.packageName) {
        uni.showToast({
          title: '请选择套餐',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.expectedFlights) {
        uni.showToast({
          title: '请输入预计航次',
          icon: 'none'
        });
        return;
      }

      // 这里可以调用API提交数据
      console.log('提交的表单数据:', this.formData);

      uni.showToast({
        title: '创建成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.add-fight-task {
  background-color: #f5f5f5;
  min-height: 100vh;
}


// 内容区域
.content {
  padding: 20px 15px;
  margin-top: 88px;

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #1989fa;
    color: #1989fa;
    background: transparent;
    font-size: 12px;

    &.active {
      background: #1989fa;
      color: #fff;
    }
  }

}
</style>
