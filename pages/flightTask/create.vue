<template>
	<view class="container">
		<!-- 自定义导航栏 -->
		<view class="nav bg-white" :style="'height:' + navH + 'px'">
			<view class="nav-title">
				<text class="nav-title-text iconfont icon-arrow-left-s-line" @click="goBack"></text>
				<text class="nav-title-text">创建飞行任务</text>
			</view>
		</view>

		<!-- 内容主体 -->
		<view class="content" :style="'height:calc(100vh - ' + navH + 'px)'" style="overflow: auto;">
			<form @submit="submitForm">
				<!-- 基本信息 -->
				<van-cell-group>
					<!-- 注册号 -->
					<van-field
						label="注册号"
						v-model="formData.registrationNumber"
						placeholder="请选择注册号"
						readonly
						is-link
						@click="showRegistrationPicker = true"
						required
					/>

					<!-- 机型 -->
					<van-field
						label="机型"
						v-model="formData.aircraftModel"
						readonly
					/>

					<!-- 任务性质 -->
					<van-field label="任务性质" required>
						<template #input>
							<view class="radio-group">
								<radio-group @change="onTaskTypeChange">
									<label class="radio-item">
										<radio value="空中游览" :checked="formData.taskType === '空中游览'" />
										<text>空中游览</text>
									</label>
									<label class="radio-item">
										<radio value="空中交通" :checked="formData.taskType === '空中交通'" />
										<text>空中交通</text>
									</label>
									<label class="radio-item">
										<radio value="其他" :checked="formData.taskType === '其他'" />
										<text>其他</text>
									</label>
								</radio-group>
							</view>
						</template>
					</van-field>

					<!-- 计划时间 -->
					<van-field label="计划时间" required>
						<template #input>
							<view class="time-group">
								<view class="time-buttons">
									<button
										type="button"
										class="time-btn"
										:class="{active: formData.planTime === '今天'}"
										@click="setPlanTime('今天')"
									>
										今天
									</button>
									<button
										type="button"
										class="time-btn"
										:class="{active: formData.planTime === '明天'}"
										@click="setPlanTime('明天')"
									>
										明天
									</button>
									<button
										type="button"
										class="time-btn"
										:class="{active: formData.planTime === '其他日期'}"
										@click="setPlanTime('其他日期')"
									>
										其他日期
									</button>
								</view>
								<picker
									v-if="formData.planTime === '其他日期'"
									mode="date"
									:value="formData.planDate"
									@change="onDateChange"
									class="date-picker"
								>
									<view class="date-display">{{ formData.planDate || '请选择日期' }}</view>
								</picker>
							</view>
						</template>
					</van-field>

					<!-- 起飞地 -->
					<van-field label="起飞地" required>
						<template #input>
							<view class="location-group">
								<button
									type="button"
									class="location-btn"
									:class="{active: formData.takeoffLocation === '复兴岛'}"
									@click="setTakeoffLocation('复兴岛')"
								>
									复兴岛
								</button>
								<button
									type="button"
									class="location-btn"
									:class="{active: formData.takeoffLocation === '龙华'}"
									@click="setTakeoffLocation('龙华')"
								>
									龙华
								</button>
								<button
									type="button"
									class="location-btn"
									:class="{active: formData.takeoffLocation === '其他'}"
									@click="setTakeoffLocation('其他')"
								>
									其他
								</button>
							</view>
						</template>
					</van-field>

					<!-- 空域产品名称 -->
					<van-field
						label="空域产品名称"
						v-model="formData.airspaceProductName"
						placeholder="请选择空域产品"
						readonly
						is-link
						@click="showAirspaceProductPicker = true"
						required
					/>

					<!-- 套餐名称 -->
					<van-field
						label="套餐名称"
						v-model="formData.packageName"
						placeholder="请选择套餐"
						readonly
						is-link
						@click="showPackagePicker = true"
						required
					/>

					<!-- 预计航次 -->
					<van-field
						label="预计航次"
						v-model="formData.expectedFlights"
						placeholder="请输入"
						type="number"
						required
					/>
				</van-cell-group>

				<!-- 计划时间明细 -->
				<van-cell-group title="计划时间明细" style="margin-top: 20px;">
					<!-- 第一架次 -->
					<view class="flight-time-row">
						<view class="flight-label">第一架次</view>
						<input
							class="time-input"
							v-model="formData.firstFlightTime"
							placeholder="请输入时间"
						/>
						<input
							class="time-input"
							v-model="formData.firstFlightEndTime"
							placeholder="请输入时间"
						/>
					</view>

					<!-- 第二架次 -->
					<view class="flight-time-row">
						<view class="flight-label">第二架次</view>
						<input
							class="time-input"
							v-model="formData.secondFlightTime"
							placeholder="请输入时间"
						/>
						<input
							class="time-input"
							v-model="formData.secondFlightEndTime"
							placeholder="请输入时间"
						/>
					</view>

					<!-- 第三架次 -->
					<view class="flight-time-row">
						<view class="flight-label">第三架次</view>
						<input
							class="time-input"
							v-model="formData.thirdFlightTime"
							placeholder="请输入时间"
						/>
						<input
							class="time-input"
							v-model="formData.thirdFlightEndTime"
							placeholder="请输入时间"
						/>
					</view>

					<!-- 起降间隔 -->
					<van-field label="起降间隔">
						<template #input>
							<view class="interval-group">
								<button
									type="button"
									class="interval-btn"
									:class="{active: formData.interval === '5分钟'}"
									@click="setInterval('5分钟')"
								>
									5分钟
								</button>
								<button
									type="button"
									class="interval-btn"
									:class="{active: formData.interval === '15分钟'}"
									@click="setInterval('15分钟')"
								>
									15分钟
								</button>
								<button
									type="button"
									class="interval-btn"
									:class="{active: formData.interval === '20分钟'}"
									@click="setInterval('20分钟')"
								>
									20分钟
								</button>
								<button
									type="button"
									class="interval-btn"
									:class="{active: formData.interval === '30分钟'}"
									@click="setInterval('30分钟')"
								>
									30分钟
								</button>
							</view>
						</template>
					</van-field>
				</van-cell-group>

				<!-- 备注 -->
				<van-cell-group title="备注" style="margin-top: 20px;">
					<van-field
						v-model="formData.remark"
						type="textarea"
						placeholder="请输入"
						autosize
						border="false"
					/>
				</van-cell-group>

				<!-- 智能解析 -->
				<van-cell-group title="智能解析" style="margin-top: 20px;">
					<van-field
						v-model="formData.intelligentAnalysis"
						type="textarea"
						placeholder="输入文本"
						autosize
						border="false"
					/>
					<view class="analysis-actions">
						<van-button
							type="primary"
							size="small"
							@click="analyzeText"
							class="analyze-btn"
						>
							解析
						</van-button>
					</view>
				</van-cell-group>

				<!-- 底部按钮 -->
				<view class="bottom-actions">
					<van-button
						type="primary"
						block
						@click="submitForm"
						class="submit-btn"
					>
						确定创建飞行计划
					</van-button>
				</view>
			</form>
		</view>

		<!-- 注册号选择器 -->
		<van-popup v-model="showRegistrationPicker" position="bottom">
			<van-picker
				:columns="registrationOptions"
				@confirm="onRegistrationConfirm"
				@cancel="showRegistrationPicker = false"
				show-toolbar
				title="选择注册号"
			/>
		</van-popup>

		<!-- 空域产品选择器 -->
		<van-popup v-model="showAirspaceProductPicker" position="bottom">
			<van-picker
				:columns="airspaceProductOptions"
				@confirm="onAirspaceProductConfirm"
				@cancel="showAirspaceProductPicker = false"
				show-toolbar
				title="选择空域产品"
			/>
		</van-popup>

		<!-- 套餐选择器 -->
		<van-popup v-model="showPackagePicker" position="bottom">
			<van-picker
				:columns="packageOptions"
				@confirm="onPackageConfirm"
				@cancel="showPackagePicker = false"
				show-toolbar
				title="选择套餐"
			/>
		</van-popup>
	</view>
</template>

<script>
export default {
	name: "createFlightTask",
	data() {
		return {
			navH: 0,
			// 表单数据
			formData: {
				registrationNumber: '', // 注册号
				aircraftModel: 'BELL429', // 机型
				taskType: '空中游览', // 任务性质
				planTime: '今天', // 计划时间
				planDate: '', // 具体日期
				takeoffLocation: '复兴岛', // 起飞地
				airspaceProductName: '', // 空域产品名称
				packageName: '', // 套餐名称
				expectedFlights: '', // 预计航次
				firstFlightTime: '', // 第一架次开始时间
				firstFlightEndTime: '', // 第一架次结束时间
				secondFlightTime: '', // 第二架次开始时间
				secondFlightEndTime: '', // 第二架次结束时间
				thirdFlightTime: '', // 第三架次开始时间
				thirdFlightEndTime: '', // 第三架次结束时间
				interval: '15分钟', // 起降间隔
				remark: '', // 备注
				intelligentAnalysis: '' // 智能解析
			},
			// 选择器显示状态
			showRegistrationPicker: false,
			showAirspaceProductPicker: false,
			showPackagePicker: false,
			// 选择器选项
			registrationOptions: [
				{ text: 'B-7613', value: 'B-7613' },
				{ text: 'B-7614', value: 'B-7614' },
				{ text: 'B-7615', value: 'B-7615' }
			],
			airspaceProductOptions: [
				{ text: '黄浦江低空旅游直升机航空游览', value: '黄浦江低空旅游直升机航空游览' },
				{ text: '上海市区观光飞行', value: '上海市区观光飞行' },
				{ text: '浦东新区航线', value: '浦东新区航线' }
			],
			packageOptions: [
				{ text: '[日间] 15分钟单人票', value: '[日间] 15分钟单人票' },
				{ text: '[日间] 30分钟双人票', value: '[日间] 30分钟双人票' },
				{ text: '[夜间] 20分钟观光票', value: '[夜间] 20分钟观光票' }
			]
		}
	},
	onLoad() {
		// 获取导航栏高度
		this.navH = getApp().globalData.navHeight || 88;
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 任务性质改变
		onTaskTypeChange(e) {
			this.formData.taskType = e.detail.value;
		},

		// 设置计划时间
		setPlanTime(time) {
			this.formData.planTime = time;
			if (time !== '其他日期') {
				this.formData.planDate = '';
			}
		},

		// 日期改变
		onDateChange(e) {
			this.formData.planDate = e.detail.value;
		},

		// 设置起飞地
		setTakeoffLocation(location) {
			this.formData.takeoffLocation = location;
		},

		// 设置起降间隔
		setInterval(interval) {
			this.formData.interval = interval;
		},

		// 注册号确认
		onRegistrationConfirm(value) {
			this.formData.registrationNumber = value.text;
			// 根据注册号设置机型（这里可以根据实际业务逻辑调整）
			if (value.value === 'B-7613') {
				this.formData.aircraftModel = 'BELL429';
			} else {
				this.formData.aircraftModel = 'BELL429';
			}
			this.showRegistrationPicker = false;
		},

		// 空域产品确认
		onAirspaceProductConfirm(value) {
			this.formData.airspaceProductName = value.text;
			this.showAirspaceProductPicker = false;
		},

		// 套餐确认
		onPackageConfirm(value) {
			this.formData.packageName = value.text;
			this.showPackagePicker = false;
		},

		// 智能解析
		analyzeText() {
			if (!this.formData.intelligentAnalysis.trim()) {
				uni.showToast({
					title: '请输入要解析的文本',
					icon: 'none'
				});
				return;
			}

			uni.showToast({
				title: '解析功能开发中',
				icon: 'none'
			});
		},

		// 提交表单
		submitForm() {
			// 表单验证
			if (!this.formData.registrationNumber) {
				uni.showToast({
					title: '请选择注册号',
					icon: 'none'
				});
				return;
			}

			if (!this.formData.airspaceProductName) {
				uni.showToast({
					title: '请选择空域产品',
					icon: 'none'
				});
				return;
			}

			if (!this.formData.packageName) {
				uni.showToast({
					title: '请选择套餐',
					icon: 'none'
				});
				return;
			}

			if (!this.formData.expectedFlights) {
				uni.showToast({
					title: '请输入预计航次',
					icon: 'none'
				});
				return;
			}

			// 这里可以调用API提交数据
			console.log('提交的表单数据:', this.formData);

			uni.showToast({
				title: '创建成功',
				icon: 'success'
			});

			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

// 导航栏样式
.nav {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	display: flex;
	align-items: flex-end;
	padding-bottom: 10px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nav-title {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	position: relative;
	font-size: 18px;
	font-weight: 600;
	color: #333;
}

.nav-title-text:first-child {
	position: absolute;
	left: 15px;
	font-size: 20px;
	cursor: pointer;
}

// 内容区域
.content {
	padding: 20px 15px;
	margin-top: 88px;
}

// 单选按钮组
.radio-group {
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
}

.radio-item {
	display: flex;
	align-items: center;
	gap: 5px;
	font-size: 14px;
}

// 时间选择按钮组
.time-group {
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.time-buttons {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.time-btn {
	padding: 8px 16px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background: #fff;
	font-size: 14px;
	color: #333;

	&.active {
		background: #1989fa;
		color: #fff;
		border-color: #1989fa;
	}
}

.date-picker {
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background: #fff;
}

.date-display {
	font-size: 14px;
	color: #333;
}

// 起飞地选择按钮组
.location-group {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.location-btn {
	padding: 8px 16px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background: #fff;
	font-size: 14px;
	color: #333;

	&.active {
		background: #1989fa;
		color: #fff;
		border-color: #1989fa;
	}
}

// 架次时间行
.flight-time-row {
	display: flex;
	align-items: center;
	padding: 15px;
	border-bottom: 1px solid #eee;
	gap: 10px;
}

.flight-label {
	width: 80px;
	font-size: 14px;
	color: #333;
	flex-shrink: 0;
}

.time-input {
	flex: 1;
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 14px;
	background: #fff;
}

// 起降间隔按钮组
.interval-group {
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.interval-btn {
	padding: 8px 16px;
	border: 1px solid #ddd;
	border-radius: 4px;
	background: #fff;
	font-size: 14px;
	color: #333;

	&.active {
		background: #1989fa;
		color: #fff;
		border-color: #1989fa;
	}
}

// 解析按钮
.analysis-actions {
	padding: 15px;
	text-align: right;
}

.analyze-btn {
	width: 80px;
}

// 底部按钮
.bottom-actions {
	padding: 30px 0;
}

.submit-btn {
	height: 50px;
	font-size: 16px;
	font-weight: 600;
}

// 通用样式
.bg-white {
	background-color: #fff;
}

// van-cell-group 标题样式调整
:deep(.van-cell-group__title) {
	padding: 16px 16px 8px;
	font-size: 14px;
	font-weight: 600;
	color: #333;
}
</style>
