<template>
  <view class="container">
    <!-- 自定义导航栏 -->
    <CustomerNav title="创建飞行任务书"/>

    <!-- 内容主体 -->
    <view class="content">
      <form @submit="submitForm">
        <van-cell-group>
          <!-- 注册号 -->
          <van-field
              required
              label="注册号"
              v-model="formData.registrationNumber"
              placeholder="请选择注册号"
              readonly
              is-link
              @click.native="openPicker('注册号', registrationOptions, 'registrationNumber')"
          />
          <!-- 机型 -->
          <van-field
              label="机型"
              v-model="formData.aircraftType"
              readonly
          />

          <!-- 任务性质 -->
          <van-cell title="任务性质" required>
            <view class="cell-group">
              <view class="btn-groups">
                <van-button
                    type="info"
                    class="time-btn"
                    :class="{active: formData.taskType === '空中游览'}"
                    @click="setPlanTime('空中游览')"
                    size="mini"
                    plain
                >
                  空中游览
                </van-button>
                <van-button
                    type="info"
                    class="time-btn"
                    :class="{active: formData.taskType === '空中交通'}"
                    @click="setPlanTime('空中交通')"
                    size="mini"
                    plain
                >
                  空中交通
                </van-button>
                <van-button
                    type="info"
                    class="time-btn"
                    :class="{active: formData.taskType === '其他'}"
                    @click="setPlanTime('其他')"
                    size="mini"
                    plain
                >
                  其他
                </van-button>
              </view>
              <picker
                  v-if="formData.taskType === '其他日期'"
                  mode="date"
                  :value="formData.planDate"
                  @change="onDateChange"
                  class="date-picker"
              >
                <view class="date-display">{{ formData.planDate || '请选择日期' }}</view>
              </picker>
            </view>
          </van-cell>

          <!-- 计划时间 -->
          <van-cell title="计划时间" required>
            <view class="time-group">
              <view class="time-buttons">
                <button
                    type="button"
                    class="time-btn"
                    :class="{active: formData.planTime === '今天'}"
                    @click="setPlanTime('今天')"
                >
                  今天
                </button>
                <button
                    type="button"
                    class="time-btn"
                    :class="{active: formData.planTime === '明天'}"
                    @click="setPlanTime('明天')"
                >
                  明天
                </button>
                <button
                    type="button"
                    class="time-btn"
                    :class="{active: formData.planTime === '其他日期'}"
                    @click="setPlanTime('其他日期')"
                >
                  其他日期
                </button>
              </view>
              <picker
                  v-if="formData.planTime === '其他日期'"
                  mode="date"
                  :value="formData.planDate"
                  @change="onDateChange"
                  class="date-picker"
              >
                <view class="date-display">{{ formData.planDate || '请选择日期' }}</view>
              </picker>
            </view>
          </van-cell>

          <!-- 起飞地 -->
          <van-cell title="起飞地" required>
            <view class="location-group">
              <button
                  type="button"
                  class="location-btn"
                  :class="{active: formData.takeoffLocation === '复兴岛'}"
                  @click="setTakeoffLocation('复兴岛')"
              >
                复兴岛
              </button>
              <button
                  type="button"
                  class="location-btn"
                  :class="{active: formData.takeoffLocation === '龙华'}"
                  @click="setTakeoffLocation('龙华')"
              >
                龙华
              </button>
              <button
                  type="button"
                  class="location-btn"
                  :class="{active: formData.takeoffLocation === '其他'}"
                  @click="setTakeoffLocation('其他')"
              >
                其他
              </button>
            </view>
          </van-cell>

          <!-- 空域产品名称 -->
          <van-field
              label="空域产品名称"
              v-model="formData.airspaceProductName"
              placeholder="请选择空域产品"
              readonly
              is-link
              @click="showAirspaceProductPicker = true"
              required
          />

          <!-- 套餐名称 -->
          <van-field
              label="套餐名称"
              v-model="formData.packageName"
              placeholder="请选择套餐"
              readonly
              is-link
              @click="showPackagePicker = true"
              required
          />

          <!-- 预计航次 -->
          <van-field
              label="预计航次"
              v-model="formData.expectedFlights"
              placeholder="请输入"
              type="number"
              required
          />
        </van-cell-group>

        <!-- 计划时间明细 -->
        <van-cell-group title="计划时间明细" style="margin-top: 20px;">
          <!-- 第一架次 -->
          <view class="flight-time-row">
            <view class="flight-label">第一架次</view>
            <input
                class="time-input"
                v-model="formData.firstFlightTime"
                placeholder="请输入时间"
            />
            <input
                class="time-input"
                v-model="formData.firstFlightEndTime"
                placeholder="请输入时间"
            />
          </view>

          <!-- 第二架次 -->
          <view class="flight-time-row">
            <view class="flight-label">第二架次</view>
            <input
                class="time-input"
                v-model="formData.secondFlightTime"
                placeholder="请输入时间"
            />
            <input
                class="time-input"
                v-model="formData.secondFlightEndTime"
                placeholder="请输入时间"
            />
          </view>

          <!-- 第三架次 -->
          <view class="flight-time-row">
            <view class="flight-label">第三架次</view>
            <input
                class="time-input"
                v-model="formData.thirdFlightTime"
                placeholder="请输入时间"
            />
            <input
                class="time-input"
                v-model="formData.thirdFlightEndTime"
                placeholder="请输入时间"
            />
          </view>

          <!-- 起降间隔 -->
          <van-cell title="起降间隔">
            <view class="interval-group">
              <button
                  type="button"
                  class="interval-btn"
                  :class="{active: formData.interval === '5分钟'}"
                  @click="setInterval('5分钟')"
              >
                5分钟
              </button>
              <button
                  type="button"
                  class="interval-btn"
                  :class="{active: formData.interval === '15分钟'}"
                  @click="setInterval('15分钟')"
              >
                15分钟
              </button>
              <button
                  type="button"
                  class="interval-btn"
                  :class="{active: formData.interval === '20分钟'}"
                  @click="setInterval('20分钟')"
              >
                20分钟
              </button>
              <button
                  type="button"
                  class="interval-btn"
                  :class="{active: formData.interval === '30分钟'}"
                  @click="setInterval('30分钟')"
              >
                30分钟
              </button>
            </view>
          </van-cell>
        </van-cell-group>

        <!-- 备注 -->
        <van-cell-group title="备注" style="margin-top: 20px;">
          <van-field
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入"
              autosize
              border="false"
          />
        </van-cell-group>

        <!-- 智能解析 -->
        <van-cell-group title="智能解析" style="margin-top: 20px;">
          <van-field
              v-model="formData.intelligentAnalysis"
              type="textarea"
              placeholder="输入文本"
              autosize
              border="false"
          />
          <view class="analysis-actions">
            <van-button
                type="primary"
                size="small"
                @click="analyzeText"
                class="analyze-btn"
            >
              解析
            </van-button>
          </view>
        </van-cell-group>

        <!-- 底部按钮 -->
        <view class="bottom-actions">
          <van-button
              type="primary"
              block
              @click="submitForm"
              class="submit-btn"
          >
            确定创建飞行计划
          </van-button>
        </view>
      </form>
    </view>

    <!-- 注册号选择器 -->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
      />
    </van-popup>

    <!--    &lt;!&ndash; 空域产品选择器 &ndash;&gt;-->
    <!--    <van-popup :show="showAirspaceProductPicker" position="bottom">-->
    <!--      <van-picker-->
    <!--          :columns="airspaceProductOptions"-->
    <!--          @confirm="onAirspaceProductConfirm"-->
    <!--          @cancel="showAirspaceProductPicker = false"-->
    <!--          show-toolbar-->
    <!--          title="选择空域产品"-->
    <!--      />-->
    <!--    </van-popup>-->

    <!--    <van-popup v-model="pickerData.show" position="bottom">-->
    <!--      <van-picker-->
    <!--          :columns="pickerData.list"-->
    <!--          @confirm="onPickerConfirm"-->
    <!--          @cancel="closePicker"-->
    <!--          show-toolbar-->
    <!--          :title="pickerData.title"-->
    <!--      />-->
    <!--    </van-popup>-->
  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";

export default {
  name: "createFlightTask",
  components: {CustomerNav},
  data() {
    return {
      // 表单数据
      formData: {
        // registrationNumber: '', // 注册号
        // aircraftType: 'BELL429', // 机型
        // taskType: '空中游览', // 任务性质
        // planTime: '今天', // 计划时间
        // planDate: '', // 具体日期
        // takeoffLocation: '复兴岛', // 起飞地
        // airspaceProductName: '', // 空域产品名称
        // packageName: '', // 套餐名称
        // expectedFlights: '', // 预计航次
        // firstFlightTime: '', // 第一架次开始时间
        // firstFlightEndTime: '', // 第一架次结束时间
        // secondFlightTime: '', // 第二架次开始时间
        // secondFlightEndTime: '', // 第二架次结束时间
        // thirdFlightTime: '', // 第三架次开始时间
        // thirdFlightEndTime: '', // 第三架次结束时间
        // interval: '15分钟', // 起降间隔
        // remark: '', // 备注
        // intelligentAnalysis: '' // 智能解析
      },
      // 选择器显示状态
      showRegistrationPicker: false,
      showAirspaceProductPicker: false,
      showPackagePicker: false,
      // 选择器选项
      registrationOptions: [
        {text: 'B-7613', value: 'B-7613'},
        {text: 'B-7614', value: 'B-7614'},
        {text: 'B-7615', value: 'B-7615'}
      ],
      airspaceProductOptions: [
        {text: '黄浦江低空旅游直升机航空游览', value: '黄浦江低空旅游直升机航空游览'},
        {text: '上海市区观光飞行', value: '上海市区观光飞行'},
        {text: '浦东新区航线', value: '浦东新区航线'}
      ],
      packageOptions: [
        {text: '[日间] 15分钟单人票', value: '[日间] 15分钟单人票'},
        {text: '[日间] 30分钟双人票', value: '[日间] 30分钟双人票'},
        {text: '[夜间] 20分钟观光票', value: '[夜间] 20分钟观光票'}
      ],
      // selectModalShow: false,
      taskTypeList: [
        {text: '空中游览', value: '空中游览'},
        {text: '空中拍照', value: '空中拍照'},
        {text: '其他', value: '其他'}
      ],//任务类型
      pickerData: {
        show: false,
        title: "",
        list: [],
        formKey: "",
      },//下拉选择数据
    }
  },

  methods: {
    openPicker(title, list, formKey) {
      console.log(11111)
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: "",
        list: [],
        formKey: "",
      }
    },
    onPickerConfirm(ev) {
      console.log(ev.detail)
      this.formData[this.pickerData.formKey] = ev.detail.value.value;
      if (this.pickerData.formKey === "registrationNumber") {
        this.formData.aircraftType = ev.detail.value.value;
      }
      this.closePicker();
    },
    // 任务性质改变
    onTaskTypeChange(e) {
      this.formData.taskType = e.detail.value;
    },

    // 设置计划时间
    setPlanTime(time) {
      this.formData.planTime = time;
      if (time !== '其他日期') {
        this.formData.planDate = '';
      }
    },

    // 日期改变
    onDateChange(e) {
      this.formData.planDate = e.detail.value;
    },

    // 设置起飞地
    setTakeoffLocation(location) {
      this.formData.takeoffLocation = location;
    },

    // 设置起降间隔
    setInterval(interval) {
      this.formData.interval = interval;
    },

    // 智能解析
    analyzeText() {
      if (!this.formData.intelligentAnalysis.trim()) {
        uni.showToast({
          title: '请输入要解析的文本',
          icon: 'none'
        });
        return;
      }

      uni.showToast({
        title: '解析功能开发中',
        icon: 'none'
      });
    },

    // 提交表单
    submitForm() {
      // 表单验证
      if (!this.formData.registrationNumber) {
        uni.showToast({
          title: '请选择注册号',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.airspaceProductName) {
        uni.showToast({
          title: '请选择空域产品',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.packageName) {
        uni.showToast({
          title: '请选择套餐',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.expectedFlights) {
        uni.showToast({
          title: '请输入预计航次',
          icon: 'none'
        });
        return;
      }

      // 这里可以调用API提交数据
      console.log('提交的表单数据:', this.formData);

      uni.showToast({
        title: '创建成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}


// 内容区域
.content {
  padding: 20px 15px;
  margin-top: 88px;

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;

  }
}


// van-cell-group 标题样式调整
:deep(.van-cell-group__title) {
  padding: 16px 16px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}
</style>
