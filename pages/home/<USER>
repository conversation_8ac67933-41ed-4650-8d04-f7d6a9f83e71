<template>
  <view class="container">
    <view class="nav bg-white" :style="'height:' + navH + 'px'">
      <view class="nav-title">首页</view>
    </view>
    <!-- <view class="logos">
      <image src="../../static/images/comLogo.png" ></image>
    </view> -->
    <view class="content">
      <!-- display: flex;
      align-items: center;
      justify -content: center;-->

      <view class="parentBox" v-for="(item, index) in wxMenus">
        <view class="outBox" @click="goTodo(item.menuUrl)" v-if="item.menuName!='扫码验票'">
          <!-- <icon :type="item.menuiconsweb" size="32" color='white' /> -->
          <van-icon :name="item.menuiconsweb" size="2.1em"/>
          <view class="title">
            {{ item.menuName }}
          </view>
        </view>
      </view>


      <!-- <view class="outBox" @click="goflightPlan">
        <icon type="info" size="15" color='white'/>
        <view class="title">
          航班计划
        </view>
      </view>
      <view class="outBox" @click="goStudyFlightPlan">
        <icon type="info" size="15" color='white'/>
        <view class="title">
          今日航班
        </view>
      </view>
      <view class="outBox" @click="goStudyOrder">
        <icon type="info" size="15" color='white'/>
        <view class="title">
          低空研学
        </view>
      </view> -->
      <!-- <view class="outBox">
        <icon type="info" size="15" color='white'/>
        <view class="title">
          培训考试
        </view>
      </view>
      <view class="outBox">
        <icon type="info" size="15" color='white'/>
        <view class="title">
          培训考试
        </view>
      </view>
      <view class="outBox">
        <icon type="info" size="15" color='white'/>
        <view class="title">
          培训考试
        </view>
      </view> -->

    </view>

    <!-- 整体背景放在最后 -->
    <view class="bg-main">
      <image class="background" src="../../static/images/background.png"></image>
    </view>
  </view>
</template>
<script>
import {
  authLogin
} from '../../api/weChat.js';

const App = getApp();
export default {
  data() {
    return {
      currentTime: '',
      homeInfo: '',
      navH: 0,
      wxMenus: [],

    };
  },
  onLoad: function (options) {
    //自定义导航
    this.$set(this.$data, 'navH', Number(App.globalData.navHeight));
  },
  onShow: function (options) {
    this.codeLogin()
    this.wxMenus = uni.getStorageSync('wxMenus')
    console.log(uni.getStorageSync('wxMenus'))
  },

  methods: {
    codeLogin() {
      let that = this;
      uni.login({
        provider: 'weixin', //使用微信登录
        success: async function (loginRes) {
          console.log(loginRes)
          let param = {
            code: loginRes.code
          };

          try {
            const data = await authLogin(param);
            console.log(data)
            uni.setStorageSync('openId', data.response.data.openId)
            // 将token存到本地存储中
            uni.setStorageSync('token', data.response.data.token);
            //将用户信息存到本地存储
            uni.setStorageSync('userInfo', data.response.data.userVO);
            if (data.response.data.wxMenus != null && data.response.data.wxMenus.length > 0) {
              let colorList = ['clock-o', 'sign', 'search', 'user-o', 'wap-home', 'comment', 'gem']
              data.response.data.wxMenus.map((i, o) => {
                if (o > 6) {
                  o -= 7
                }
                i['menuiconsweb'] = colorList[o]
              })
            }
            console.log(data.response.data.wxMenus)
            uni.setStorageSync('wxMenus', data.response.data.wxMenus);
            uni.setStorageSync('userRole', data.response.data.userRole || '');
            that.wxMenus = data.response.data.wxMenus
            if (data.response.data.authStatus == 0 || data.response.data.authStatus == 2) {
              uni.showToast({
                title: '暂无权限，跳转至申请页面！',
                icon: 'none'
              })
              setTimeout(() => {
                uni.navigateTo({
                  url: '/pages/login/approal'
                })
              }, 1000)
            }
          } catch (e) {
            uni.showToast({
              title: '获取失败，请稍后再试',
              icon: 'none',
            })
          }
        }
      });
    },


    // goflightPlan(){
    // 	uni.navigateTo({
    // 	    url: '/pages/flightPlan/index'
    // 	});
    // },
    // goStudyFlightPlan(){
    // 	uni.navigateTo({
    // 	    url: '/pages/study/flightPlan/index'
    // 	});
    // },
    // goStudyOrder(){
    // 	uni.navigateTo({
    // 	    url: '/pages/study/order/index'
    // 	});
    // },
    goTodo(url) {
      console.log(url)
      if (url == null || url == '') {
        uni.showModal({
          content: "菜单地址为空"
        })
        return false;
      }
      uni.navigateTo({
        url: url
      });
    }


  }
};
</script>

<style lang="scss" scoped>
.content {
  overflow: hidden;
  width: 90%;
  margin: 10px auto;

  .title {
    margin-top: 7px;
    font-size: 28rpx;
    font-weight: 700;
  }

  .outBox {
    width: 48%;
    height: 145px;
    background: #2196f3;
    border-radius: 8px;
    display: flex;
    /* vertical-align: middle; */
    /* display: block; */
    align-items: center;
    justify-content: center;
    flex-direction: column;
    color: white;
    float: left;
    margin: 1%;
    // flex-wrap: wrap

  }

  // .parentBox:first-child .outBox{
  // 	background-color: #ffc107;
  // }
}

page {
  height: 100%;
}

.background {
  width: 100%;
  height: 100%;
  background-size: 100% 100%;
  z-index: -1;
  position: absolute;
  top: 0px;
  bottom: 0px;
}

.main {
  width: 100%;
  height: 100%;
}

.nav {
  width: 100%;
  overflow: hidden;
  position: relative;
  top: 0;
  left: 0;
  z-index: 10;
}

.nav-title {
  width: 100%;
  /* height: 45px;
  line-height: 45px; */
  text-align: center;
  position: absolute;
  bottom: 16px;
  left: 0;
  z-index: 10;
  font-family: SF Pro Text;
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  color: #000000;
}

.refresh {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  top: 0;
  width: 100%;
  height: 100%;
  background-color: #eeeeee;
  color: #000000;
}

.content {
  // padding: 0 16px;
}

.refresh-container {
  width: 750rpx;
  text-align: center;
  position: absolute;
  align-items: center;
  margin-bottom: 20px;
}

.refresh-container text {
}

.no-data-box {
  background: rgba(255, 255, 255, 0.64);
  height: 343px;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .image {
    width: 140px;
    height: 140px;
  }

  .col-gray-500 {
    font-size: 14px;
  }
}

.spinning {
  margin-right: 4px;
  display: inline-block;
  -webkit-animation: rotate 1s linear infinite;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.logos {
  width: 80%;
  height: 60px;
  margin: 0 auto;
  display: block;
  border-radius: 5px;
  overflow: hidden;
}

.logos image {
  width: 100%;
  height: 100%;
}
</style>
