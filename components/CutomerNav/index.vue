<template>
  <view class="nav bg-white" :style="'height:'+ navH +'px;'+ style">
    <view class="nav-title">
      <text class="nav-title-text iconfont icon-arrow-left-s-line" @click="handleGoBack()"></text>
      <text class="nav-title-text">{{ title }}</text>
    </view>
  </view>
</template>
<script>
export default {
  name: "CustomerNav",
  props: {
    goBack: Function, // 返回上一页
    title: String, // 导航栏标题
    style: String, // 导航栏样式
  },
  data() {
    return {
      navH: 0,
    }
  },
  created() {
    // 获取导航栏高度
    this.navH = getApp().globalData.navHeight || 88;
  },
  methods: {
    // 返回上一页
    handleGoBack() {
      if (this.goBack) {
        this.goBack();
      } else {
        uni.navigateBack();
      }
    },
  }

}
</script>


<style scoped lang="scss">
// 导航栏样式
.nav {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  display: flex;
  align-items: flex-end;
  padding-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: #fff;
}

.nav-title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.nav-title-text:first-child {
  position: absolute;
  left: 15px;
  font-size: 20px;
  cursor: pointer;
}
</style>
