{
  "pages": [
    {
      "path": "pages/login/empty",
      "style": {
        "navigationBarTitleText": "空白页面"
        //token登录访问
      }
    },
    {
      "path": "pages/login/approal",
      "style": {
        "navigationBarTitleText": "申请页面"
        //token登录访问
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录页面"
      }
    },
    {
      "path": "pages/todo/safemaintenanceDerect",
      "style": {
        "navigationBarTitleText": "保障员直接准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/safemaintenanceDerectCheck",
      "style": {
        "navigationBarTitleText": "查看保障员直接准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/login/visit",
      "style": {
        "navigationBarTitleText": "体验页面",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/maintenanceDerect",
      "style": {
        "navigationBarTitleText": "放行员直接准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/captionDerect",
      "style": {
        "navigationBarTitleText": "机长直接准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/daben",
      "style": {
        "navigationBarTitleText": "保障员上传大本",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/commentary",
      "style": {
        "navigationBarTitleText": "机长飞行讲评",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/copolitDerect",
      "style": {
        "navigationBarTitleText": "副驾驶直接准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/studyPre",
      "style": {
        "navigationBarTitleText": "飞行学习",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/my/index",
      "style": {
        "navigationBarTitleText": "我的"
      }
    },
    {
      "path": "pages/flightPlan/message",
      "style": {
        "navigationBarTitleText": "机组信息"
      }
    },
    {
      "path": "pages/my/approlDate",
      "style": {
        "navigationBarTitleText": "个人信息"
      }
    },
    // {
    // 	"path": "pages/flyTask/index",
    // 	"style": {
    // 		"navigationBarTitleText": "飞行任务",
    // 		"onReachBottonDistance":50
    // 	}
    // },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "我的待办",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/my/record",
      "style": {
        "navigationBarTitleText": "我的记录"
      }
    },
    {
      "path": "pages/my/flyTraining",
      "style": {
        "navigationBarTitleText": "飞前考试题库训练"
      }
    },
    {
      "path": "pages/todo/flyTraining",
      "style": {
        "navigationBarTitleText": "飞前考试"
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/flightPlan/flight",
      "style": {
        "navigationBarTitleText": "班次",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/flightPlan/index",
      "style": {
        "navigationBarTitleText": "航班计划",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/advance",
      "style": {
        "navigationBarTitleText": "预先准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/advanceCheck",
      "style": {
        "navigationBarTitleText": "查看预先准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/givenFlight",
      "style": {
        "navigationBarTitleText": "确认飞行时间",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/advanceAprrol",
      "style": {
        "navigationBarTitleText": "预先准备审批",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/emergencyAprrol",
      "style": {
        "navigationBarTitleText": "特殊情况",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/emergency",
      "style": {
        "navigationBarTitleText": "副驾驶上传任务书",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/commitments",
      "style": {
        "navigationBarTitleText": "副驾驶填写飞行时间",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/deliveryDerect",
      "style": {
        "navigationBarTitleText": "责任运控直接准备",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/todo/evaluation",
      "style": {
        "navigationBarTitleText": "飞行后讲评",
        "onReachBottonDistance": 50
      }
    },
    {
      "path": "pages/flightPlan/detail",
      "style": {
        "navigationBarTitleText": "航班详情"
      }
    },
    // {
    //      "path": "pages/my/scoreResult/scoreResult",
    //      "style": {
    //        "navigationBarTitleText": "训练结果"
    //      }
    //    },
    // {
    // 	"path": "pages/flyTask/taskDetail",
    // 	"style": {
    // 		"navigationBarTitleText": "飞行任务详情"
    // 	}
    // },

    {
      "path": "pages/searchFile/index",
      "style": {
        "navigationBarTitleText": "文件查询"
      }
    },
    {
      "path": "pages/todo/captionDerectCheck",
      "style": {
        "navigationBarTitleText": "查询"
      }
    },
    {
      "path": "pages/todo/copolitDerectCheck",
      "style": {
        "navigationBarTitleText": "查询"
      }
    },
    {
      "path": "pages/todo/maintenanceDerectCheck",
      "style": {
        "navigationBarTitleText": "查询"
      }
    },
    {
      "path": "pages/todo/deliveryDerectCheck",
      "style": {
        "navigationBarTitleText": "查询"
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "飞前问答"
      }
    },
    {
      "path": "pages/scan/passengerDetail",
      "style": {
        "navigationBarTitleText": "旅客详情"
      }
    },
    {
      "path": "pages/study/flightPlan/index",
      "style": {
        "navigationBarTitleText": "今日航班"
      }
    },
    {
      "path": "pages/study/order/index",
      "style": {
        "navigationBarTitleText": "低空研学"
      }
    },
    {
      "path": "pages/study/order/detail",
      "style": {
        "navigationBarTitleText": "低空研学 订单详情 旅客列表"
      }
    },
    {
      "path": "pages/study/order/add",
      "style": {
        "navigationBarTitleText": "低空研学 添加旅客"
      }
    },
    {
      "path": "pages/study/passenger/index",
      "style": {
        "navigationBarTitleText": "旅客列表"
      }
    },
    {
      "path": "pages/study/sortie/index",
      "style": {
        "navigationBarTitleText": "架次管理"
      }
    },
    {
      "path": "pages/study/sortie/details",
      "style": {
        "navigationBarTitleText": "架次详情"
      }
    },
    {
      "path": "pages/scan/index",
      "style": {
        "navigationBarTitleText": "扫码验票"
      }
    },
    {
      "path": "pages/flightTask/create",
      "style": {
        "navigationBarTitleText": "创建飞行任务书"
      }
    }
    //{ 	"path": "pages/home/<USER>",
    // 	"style": {
    // 		"navigationBarTitleText": "气象资料"
    // 	}
    // }, {
    // 	"path": "pages/home/<USER>",
    // 	"style": {
    // 		"navigationBarTitleText": "电子舱单"
    // 	}
    // }, {
    // 	"path": "pages/home/<USER>",
    // 	"style": {
    // 		"navigationBarTitleText": "放行单"
    // 	}
    // }, {
    // 	"path": "pages/home/<USER>",
    // 	"style": {
    // 		"navigationBarTitleText": "飞行时刻"
    // 	}
    // }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "uni-app",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8",
    "navigationStyle": "custom",
    "app-plus": {
      "background": "#efeff4"
    },
    "usingComponents": {
      "ly-tree-node": "/components/ly-tree/ly-tree-node",
      "van-button": "/wxcomponents/vant/button/index",
      "van-search": "/wxcomponents/vant/search/index",
      "van-dropdown-menu": "/wxcomponents/vant/dropdown-menu/index",
      "van-dropdown-item": "/wxcomponents/vant/dropdown-item/index",
      "van-card": "/wxcomponents/vant/card/index",
      "van-tag": "/wxcomponents/vant/tag/index",
      "van-icon": "/wxcomponents/vant/icon/index",
      "van-field": "/wxcomponents/vant/field/index",
      "van-popup": "/wxcomponents/vant/popup/index",
      "van-overlay": "/wxcomponents/vant/overlay/index",
      "van-transition": "/wxcomponents/vant/transition/index",
      "van-cell": "/wxcomponents/vant/cell/index",
      "van-tabbar": "/wxcomponents/vant/tabbar/index",
      "van-tabbar-item": "/wxcomponents/vant/tabbar-item/index",
      "van-divider": "/wxcomponents/vant/divider/index",
      "van-calendar": "/wxcomponents/vant/calendar/index",
      "van-notice-bar": "/wxcomponents/vant/notice-bar/index",
      "van-action-sheet": "/wxcomponents/vant/action-sheet/index",
      "van-picker": "/wxcomponents/vant/picker/index",
      "van-toast": "/wxcomponents/vant/toast/index",
      "van-area": "/wxcomponents/vant/area/index",
      "van-checkbox-group": "/wxcomponents/vant/checkbox-group/index",
      "van-checkbox": "/wxcomponents/vant/checkbox/index",
      "van-tab": "/wxcomponents/vant/tab/index",
      "van-tabs": "/wxcomponents/vant/tabs/index",
      "van-radio-group": "/wxcomponents/vant/radio-group/index",
      "van-radio": "/wxcomponents/vant/radio/index",
      "van-uploader": "/wxcomponents/vant/uploader/index",
      "van-collapse": "/wxcomponents/vant/collapse/index",
      "van-collapse-item": "/wxcomponents/vant/collapse-item/index",
      "van-datetime-picker": "/wxcomponents/vant/datetime-picker/index"
    }
  },
  "tabBar": {
    "color": "#646566",
    "selectedColor": "#007AFF",
    "borderStyle": "white",
    "backgroundColor": "rgba(255, 255, 255, 0.48)",
    "list": [
      // {
      // 	"pagePath": "pages/todo/studyPre",
      // 	"text": "预先学习",
      // 	"iconPath": "static/images/tab2_off.png",
      // 	"selectedIconPath": "static/images/tab2_on.png"
      // },
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "static/images/tab1_off.png",
        "selectedIconPath": "static/images/tab1_on.png"
      },
      {
        "pagePath": "pages/scan/index",
        "text": "扫码验票",
        "iconPath": "static/images/scan.png",
        "selectedIconPath": "static/images/scanChack.png"
      },
      {
        "pagePath": "pages/my/index",
        "text": "我的",
        "iconPath": "static/images/tab4_off.png",
        "selectedIconPath": "static/images/tab4_on.png"
      }
    ]
  }
}
